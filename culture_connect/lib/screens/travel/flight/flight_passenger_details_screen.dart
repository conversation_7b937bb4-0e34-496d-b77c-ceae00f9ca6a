// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/providers/travel/flight_providers.dart';
// import 'package:culture_connect/screens/travel/flight/flight_seat_selection_screen.dart';
import 'package:culture_connect/widgets/common/date_picker_field.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

import 'package:culture_connect/utils/validation_utils.dart';

/// Screen for collecting passenger details for flight booking
class FlightPassengerDetailsScreen extends ConsumerStatefulWidget {
  /// Creates a new flight passenger details screen
  const FlightPassengerDetailsScreen({super.key});

  @override
  ConsumerState<FlightPassengerDetailsScreen> createState() =>
      _FlightPassengerDetailsScreenState();
}

class _FlightPassengerDetailsScreenState
    extends ConsumerState<FlightPassengerDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // Current passenger index
  int _currentPassengerIndex = 0;

  // Form controllers for each passenger
  final List<Map<String, TextEditingController>> _controllers = [];
  final List<Map<String, dynamic>> _passengerData = [];

  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePassengerData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _disposeControllers();
    super.dispose();
  }

  void _initializePassengerData() {
    final passengerInfo = ref.read(passengerInfoProvider);

    for (int i = 0; i < passengerInfo.length; i++) {
      final passenger = passengerInfo[i];

      // Initialize controllers
      _controllers.add({
        'firstName': TextEditingController(text: passenger.firstName),
        'lastName': TextEditingController(text: passenger.lastName),
        'passportNumber': TextEditingController(text: passenger.passportNumber),
        'nationality': TextEditingController(text: passenger.nationality),
        'specialAssistanceDetails':
            TextEditingController(text: passenger.specialAssistanceDetails),
      });

      // Initialize data
      _passengerData.add({
        'type': passenger.type,
        'dateOfBirth': passenger.dateOfBirth,
        'gender': passenger.gender,
        'passportExpiryDate': passenger.passportExpiryDate,
        'specialAssistance': passenger.specialAssistance,
        'seatPreference': passenger.seatPreference,
        'mealPreference': passenger.mealPreference,
      });
    }
  }

  void _disposeControllers() {
    for (final controllerMap in _controllers) {
      for (final controller in controllerMap.values) {
        controller.dispose();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final passengerInfo = ref.watch(passengerInfoProvider);

    if (passengerInfo.isEmpty) {
      return Scaffold(
        appBar: AppBar(title: const Text('Passenger Details')),
        body: const Center(
          child: ErrorView(
            error:
                'No passenger information found. Please go back and search for flights.',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Passenger ${_currentPassengerIndex + 1} of ${passengerInfo.length}'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
      ),
      body: _errorMessage != null
          ? Center(
              child: ErrorView(
                error: _errorMessage!,
                onRetry: () => setState(() => _errorMessage = null),
              ),
            )
          : _buildPassengerForm(theme),
      bottomNavigationBar: _buildNavigationBar(theme),
    );
  }

  Widget _buildPassengerForm(ThemeData theme) {
    return Form(
      key: _formKey,
      child: PageView.builder(
        controller: _pageController,
        itemCount: _controllers.length,
        onPageChanged: (index) {
          setState(() => _currentPassengerIndex = index);
        },
        itemBuilder: (context, index) => _buildPassengerFormPage(theme, index),
      ),
    );
  }

  Widget _buildPassengerFormPage(ThemeData theme, int index) {
    final passenger = _passengerData[index];
    final controllers = _controllers[index];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPassengerTypeCard(theme, index),
          const SizedBox(height: 24),
          _buildPersonalInfoSection(theme, controllers, passenger),
          const SizedBox(height: 24),
          _buildPassportSection(theme, controllers, passenger),
          const SizedBox(height: 24),
          _buildPreferencesSection(theme, passenger),
          const SizedBox(height: 24),
          _buildSpecialAssistanceSection(theme, controllers, passenger),
          const SizedBox(height: 100), // Space for bottom navigation
        ],
      ),
    );
  }

  Widget _buildPassengerTypeCard(ThemeData theme, int index) {
    final passengerInfo = ref.watch(passengerInfoProvider);
    final passenger = passengerInfo[index];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _getPassengerTypeIcon(passenger.type),
              color: theme.colorScheme.primary,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Passenger ${index + 1}',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _getPassengerTypeLabel(passenger.type),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection(
    ThemeData theme,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> passenger,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personal Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controllers['firstName'],
                    decoration: const InputDecoration(
                      labelText: 'First Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => ValidationUtils.validateRequired(
                        value,
                        fieldName: 'First name'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: controllers['lastName'],
                    decoration: const InputDecoration(
                      labelText: 'Last Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => ValidationUtils.validateRequired(
                        value,
                        fieldName: 'Last name'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DatePickerField(
                    label: 'Date of Birth',
                    initialDate: passenger['dateOfBirth'],
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                    onDateSelected: (date) {
                      setState(() => passenger['dateOfBirth'] = date);
                    },
                    validator: (date) {
                      if (date == null) return 'Please select date of birth';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: passenger['gender'],
                    decoration: const InputDecoration(
                      labelText: 'Gender',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'M', child: Text('Male')),
                      DropdownMenuItem(value: 'F', child: Text('Female')),
                      DropdownMenuItem(value: 'X', child: Text('Other')),
                    ],
                    onChanged: (value) {
                      setState(() => passenger['gender'] = value);
                    },
                    validator: (value) {
                      if (value == null) return 'Please select gender';
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPassportSection(
    ThemeData theme,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> passenger,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passport Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controllers['passportNumber'],
              decoration: const InputDecoration(
                labelText: 'Passport Number',
                border: OutlineInputBorder(),
              ),
              validator: (value) => ValidationUtils.validateRequired(value,
                  fieldName: 'Passport number'),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controllers['nationality'],
                    decoration: const InputDecoration(
                      labelText: 'Nationality',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => ValidationUtils.validateRequired(
                        value,
                        fieldName: 'Nationality'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DatePickerField(
                    label: 'Passport Expiry',
                    initialDate: passenger['passportExpiryDate'],
                    firstDate: DateTime.now(),
                    lastDate:
                        DateTime.now().add(const Duration(days: 365 * 10)),
                    onDateSelected: (date) {
                      setState(() => passenger['passportExpiryDate'] = date);
                    },
                    validator: (date) {
                      if (date == null) return 'Please select expiry date';
                      if (date.isBefore(
                          DateTime.now().add(const Duration(days: 180)))) {
                        return 'Passport must be valid for at least 6 months';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection(
      ThemeData theme, Map<String, dynamic> passenger) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preferences',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<SeatPreference>(
              value: passenger['seatPreference'],
              decoration: const InputDecoration(
                labelText: 'Seat Preference',
                border: OutlineInputBorder(),
              ),
              items: SeatPreference.values.map((preference) {
                return DropdownMenuItem(
                  value: preference,
                  child: Text(preference.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => passenger['seatPreference'] = value);
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<MealPreference>(
              value: passenger['mealPreference'],
              decoration: const InputDecoration(
                labelText: 'Meal Preference',
                border: OutlineInputBorder(),
              ),
              items: MealPreference.values.map((preference) {
                return DropdownMenuItem(
                  value: preference,
                  child: Text(preference.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => passenger['mealPreference'] = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialAssistanceSection(
    ThemeData theme,
    Map<String, TextEditingController> controllers,
    Map<String, dynamic> passenger,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Special Assistance',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Requires Special Assistance'),
              subtitle: const Text('Wheelchair, medical assistance, etc.'),
              value: passenger['specialAssistance'] ?? false,
              onChanged: (value) {
                setState(() => passenger['specialAssistance'] = value);
              },
            ),
            if (passenger['specialAssistance'] == true) ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: controllers['specialAssistanceDetails'],
                decoration: const InputDecoration(
                  labelText: 'Special Assistance Details',
                  hintText: 'Please describe the assistance needed',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (passenger['specialAssistance'] == true &&
                      (value == null || value.trim().isEmpty)) {
                    return 'Please provide details about the special assistance needed';
                  }
                  return null;
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationBar(ThemeData theme) {
    final isLastPassenger = _currentPassengerIndex == _controllers.length - 1;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadowithAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentPassengerIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPassenger,
                child: const Text('Previous'),
              ),
            ),
          if (_currentPassengerIndex > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : (isLastPassenger
                      ? _proceedToSeatSelection
                      : _nextPassenger),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colorshite,
                      ),
                    )
                  : Text(
                      isLastPassenger ? 'Continue to Seat Selection' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getPassengerTypeIcon(PassengerType type) {
    switch (type) {
      case PassengerType.adult:
        return Icons.person;
      case PassengerType.child:
        return Icons.child_care;
      case PassengerType.infant:
        return Icons.baby_changing_station;
    }
  }

  String _getPassengerTypeLabel(PassengerType type) {
    switch (type) {
      case PassengerType.adult:
        return 'Adult (12+ years)';
      case PassengerType.child:
        return 'Child (2-11 years)';
      case PassengerType.infant:
        return 'Infant (under 2 years)';
    }
  }

  void _previousPassenger() {
    if (_currentPassengerIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPassenger() {
    if (_validateCurrentPassenger()) {
      _saveCurrentPassengerData();
      if (_currentPassengerIndex < _controllers.length - 1) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  bool _validateCurrentPassenger() {
    return _formKey.currentState?.validate() ?? false;
  }

  void _saveCurrentPassengerData() {
    final controllers = _controllers[_currentPassengerIndex];
    final passenger = _passengerData[_currentPassengerIndex];

    final updatedPassenger = PassengerInfo(
      type: passenger['type'],
      firstName: controllers['firstName']!.text.trim(),
      lastName: controllers['lastName']!.text.trim(),
      dateOfBirth: passenger['dateOfBirth'],
      gender: passenger['gender'],
      passportNumber: controllers['passportNumber']!.text.trim(),
      passportExpiryDate: passenger['passportExpiryDate'],
      nationality: controllers['nationality']!.text.trim(),
      specialAssistance: passenger['specialAssistance'] ?? false,
      specialAssistanceDetails:
          controllers['specialAssistanceDetails']!.text.trim(),
      seatPreference: passenger['seatPreference'] ?? SeatPreference.none,
      mealPreference: passenger['mealPreference'] ?? MealPreference.none,
    );

    ref
        .read(passengerInfoProvider.notifier)
        .updatePassenger(_currentPassengerIndex, updatedPassenger);
  }

  Future<void> _proceedToSeatSelection() async {
    if (!_validateCurrentPassenger()) return;

    setState(() => _isLoading = true);

    try {
      // Save current passenger data
      _saveCurrentPassengerData();

      // Validate all passengers are complete
      final allPassengers = ref.read(passengerInfoProvider);
      for (final passenger in allPassengers) {
        if (!passenger.isComplete) {
          throw Exception('Please complete all passenger information');
        }
      }

      if (mounted) {
        // TODO: Navigate to FlightSeatSelectionScreen when implemented
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Passenger details saved! Proceeding to seat selection...'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _errorMessage = e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
