import 'package:flutter/material.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/providers/travel/transfer/transfer_provider.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_booking_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/error_state.dart';
import 'package:culture_connect/widgets/common/image_viewer.dart';
import 'package:culture_connect/widgets/common/rating_stars.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

// Type aliases to make the code more readable
typedef TransferService = models.TransferService;
typedef TransferVehicleType = models.TransferVehicleType;
typedef TransferDriver = models.TransferDriver;

/// A screen for displaying transfer details
class TransferDetailsScreen extends StatefulWidget {
  /// The ID of the transfer to display
  final String transferId;

  /// Creates a new transfer details screen
  const TransferDetailsScreen({
    super.key,
    required this.transferId,
  });

  @override
  State<TransferDetailsScreen> createState() => _TransferDetailsScreenState();
}

class _TransferDetailsScreenState extends State<TransferDetailsScreen> {
  TransferService? _transfer;
  bool _isLoading = true;
  String? _error;
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();
    _loadTransfer();
  }

  /// Load the transfer
  Future<void> _loadTransfer() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final transferProvider =
          Provider.of<TransferProvider>(context, listen: false);
      final transfer = await transferProvider.getTransfer(widget.transferId);

      if (!mounted) return;

      setState(() {
        _transfer = transfer;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferDetailsScreen',
        'Failed to load transfer: ${widget.transferId}',
        e,
        stackTrace,
      );

      if (!mounted) return;

      setState(() {
        _error = 'Failed to load transfer: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? ErrorState(
                  message: _error!,
                  onRetry: _loadTransfer,
                )
              : _transfer != null
                  ? _buildTransferDetails()
                  : const Center(
                      child: Text('Transfer not found'),
                    ),
      bottomNavigationBar: _transfer != null ? _buildBottomBar() : null,
    );
  }

  /// Build the transfer details
  Widget _buildTransferDetails() {
    return CustomScrollView(
      slivers: [
        _buildAppBar(),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildDescription(),
                const SizedBox(height: 24),
                _buildVehicleDetails(),
                const SizedBox(height: 24),
                if (_transfer!.driver != null) ...[
                  _buildDriverDetails(),
                  const SizedBox(height: 24),
                ],
                _buildServiceDetails(),
                const SizedBox(height: 24),
                _buildCancellationPolicy(),
                const SizedBox(height: 24),
                _buildMap(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the app bar
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ImageViewer(
                      imageUrls: [
                        _transfer!.imageUrl,
                        ..._transfer!.additionalImages
                      ],
                      initialIndex: 0,
                    ),
                  ),
                );
              },
              child: Image.network(
                _transfer!.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: AppColors.surface,
                    child: const Center(
                      child: Icon(
                        Icons.airport_shuttle,
                        size: 64,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black
                        .withAlpha(179), // Equivalent to withOpacity(0.7)
                  ],
                  stops: const [0.7, 1.0],
                ),
              ),
            ),
            if (_transfer!.isOnSale)
              Positioned(
                top: 16,
                left: 16,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.error,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${_transfer!.discountPercentage?.toStringAsFixed(0)}% OFF',
                    style: AppTextStyles.subtitle2.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: _shareTransfer,
          tooltip: 'Share',
        ),
      ],
    );
  }

  /// Build the header
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _transfer!.name,
          style: AppTextStyles.headline5.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(
              Icons.location_on,
              size: 16,
              color: AppColors.primary,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _transfer!.location,
                style: AppTextStyles.body2,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            RatingStars(
              rating: _transfer!.rating,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              '${_transfer!.rating} (${_transfer!.reviewCount} reviews)',
              style: AppTextStyles.body2,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            if (_transfer!.isOnSale) ...[
              Text(
                '${_transfer!.currency}${_transfer!.originalPrice?.toStringAsFixed(2)}',
                style: AppTextStyles.body1.copyWith(
                  color: AppColors.textSecondary,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              '${_transfer!.currency}${_transfer!.price.toStringAsFixed(2)}',
              style: AppTextStyles.headline6.copyWith(
                fontWeight: FontWeight.bold,
                color: _transfer!.isOnSale
                    ? AppColors.error
                    : AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the description
  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 8),
        Text(
          _transfer!.description,
          style: AppTextStyles.body2,
        ),
      ],
    );
  }

  /// Build the vehicle details
  Widget _buildVehicleDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vehicle Details',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.primary
                            .withAlpha(25), // Equivalent to withOpacity(0.1)
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getVehicleIcon(_transfer!.vehicleType),
                        color: AppColors.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _transfer!.vehicle.fullName,
                            style: AppTextStyles.subtitle1.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _transfer!.vehicle.color,
                            style: AppTextStyles.body2,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildVehicleFeature(
                        icon: Icons.people,
                        title: 'Passengers',
                        value: _transfer!.passengerCapacity.toString(),
                      ),
                    ),
                    Expanded(
                      child: _buildVehicleFeature(
                        icon: Icons.luggage,
                        title: 'Luggage',
                        value: _transfer!.luggageCapacity.toString(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    if (_transfer!.vehicle.hasAirConditioning)
                      _buildFeatureChip(
                        icon: Icons.ac_unit,
                        label: 'A/C',
                      ),
                    if (_transfer!.vehicle.hasWifi)
                      _buildFeatureChip(
                        icon: Icons.wifi,
                        label: 'WiFi',
                      ),
                    if (_transfer!.vehicle.hasUsb)
                      _buildFeatureChip(
                        icon: Icons.usb,
                        label: 'USB',
                      ),
                    if (_transfer!.vehicle.hasChildSeat)
                      _buildFeatureChip(
                        icon: Icons.child_care,
                        label: 'Child Seat',
                      ),
                    if (_transfer!.vehicle.hasWheelchairAccess)
                      _buildFeatureChip(
                        icon: Icons.accessible,
                        label: 'Wheelchair Access',
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the driver details
  Widget _buildDriverDetails() {
    final driver = _transfer!.driver!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Driver',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 32,
                  backgroundImage: NetworkImage(driver.photoUrl),
                  backgroundColor: AppColors.surface,
                  child: const Icon(
                    Icons.person,
                    size: 32,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver.name,
                        style: AppTextStyles.subtitle1.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          RatingStars(
                            rating: driver.rating,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '(${driver.reviewCount})',
                            style: AppTextStyles.caption,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Experience: ${driver.formattedYearsOfExperience}',
                        style: AppTextStyles.body2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Languages: ${driver.formattedLanguages}',
                        style: AppTextStyles.body2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the service details
  Widget _buildServiceDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Details',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                _buildServiceFeature(
                  icon: Icons.person,
                  title: 'Service Type',
                  value: _transfer!.isPrivate ? 'Private' : 'Shared',
                ),
                const SizedBox(height: 12),
                _buildServiceFeature(
                  icon: Icons.emoji_people,
                  title: 'Meet & Greet',
                  value: _transfer!.includesMeetAndGreet
                      ? 'Included'
                      : 'Not included',
                ),
                const SizedBox(height: 12),
                _buildServiceFeature(
                  icon: Icons.flight,
                  title: 'Flight Tracking',
                  value: _transfer!.includesFlightTracking
                      ? 'Included'
                      : 'Not included',
                ),
                const SizedBox(height: 12),
                _buildServiceFeature(
                  icon: Icons.access_time,
                  title: 'Free Waiting Time',
                  value: _transfer!.formattedFreeWaitingTime,
                ),
                const SizedBox(height: 12),
                _buildServiceFeature(
                  icon: Icons.access_time_filled,
                  title: 'Availability',
                  value:
                      _transfer!.isAvailable24Hours ? '24/7' : 'Limited hours',
                ),
                const SizedBox(height: 12),
                _buildServiceFeature(
                  icon: Icons.schedule,
                  title: 'Minimum Notice',
                  value: _transfer!.formattedMinimumNotice,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the cancellation policy
  Widget _buildCancellationPolicy() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cancellation Policy',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.success
                            .withAlpha(25), // Equivalent to withOpacity(0.1)
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: AppColors.success,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        _transfer!.formattedFreeCancellation,
                        style: AppTextStyles.subtitle2.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  _transfer!.cancellationPolicy,
                  style: AppTextStyles.body2,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the map
  Widget _buildMap() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(
                  _transfer!.coordinates.latitude,
                  _transfer!.coordinates.longitude,
                ),
                zoom: 14,
              ),
              markers: {
                Marker(
                  markerId: const MarkerId('transfer'),
                  position: LatLng(
                    _transfer!.coordinates.latitude,
                    _transfer!.coordinates.longitude,
                  ),
                  infoWindow: InfoWindow(
                    title: _transfer!.name,
                    snippet: _transfer!.location,
                  ),
                ),
              },
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              myLocationButtonEnabled: false,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the bottom bar
  Widget _buildBottomBar() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25), // Equivalent to withOpacity(0.1)
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Price',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (_transfer!.isOnSale) ...[
                      Text(
                        '${_transfer!.currency}${_transfer!.originalPrice?.toStringAsFixed(2)}',
                        style: AppTextStyles.body2.copyWith(
                          color: AppColors.textSecondary,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Text(
                      '${_transfer!.currency}${_transfer!.price.toStringAsFixed(2)}',
                      style: AppTextStyles.headline6.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _transfer!.isOnSale
                            ? AppColors.error
                            : AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TransferBookingScreen(
                    transferId: _transfer!.id,
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Book Now'),
          ),
        ],
      ),
    );
  }

  /// Build a vehicle feature
  Widget _buildVehicleFeature({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.body1.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build a service feature
  Widget _buildServiceFeature({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary
                .withAlpha(25), // Equivalent to withOpacity(0.1)
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 16,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: AppTextStyles.body2.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build a feature chip
  Widget _buildFeatureChip({
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTextStyles.caption,
          ),
        ],
      ),
    );
  }

  /// Get the icon for a vehicle type
  IconData _getVehicleIcon(TransferVehicleType type) {
    switch (type) {
      case TransferVehicleType.sedan:
      case TransferVehicleType.luxurySedan:
      case TransferVehicleType.suv:
      case TransferVehicleType.luxurySuv:
      case TransferVehicleType.limousine:
        return Icons.directions_car;
      case TransferVehicleType.minivan:
      case TransferVehicleType.van:
        return Icons.airport_shuttle;
      case TransferVehicleType.shuttleBus:
        return Icons.directions_bus;
      case TransferVehicleType.electric:
        return Icons.electric_car;
    }
  }

  /// Share the transfer
  Future<void> _shareTransfer() async {
    try {
      final text = 'Check out this airport transfer: ${_transfer!.name}\n'
          'Price: ${_transfer!.currency}${_transfer!.price.toStringAsFixed(2)}\n'
          'Location: ${_transfer!.location}\n'
          'Vehicle: ${_transfer!.vehicle.fullName}\n'
          'Provider: ${_transfer!.provider}\n';

      await Share.share(text, subject: _transfer!.name);
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferDetailsScreen',
        'Failed to share transfer',
        e,
        stackTrace,
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to share: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
